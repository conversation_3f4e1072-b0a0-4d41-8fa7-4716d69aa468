'use client'

import { useState, useRef, useEffect } from 'react'
import { logger } from '@/lib/services/logger';

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card as _Card, CardContent as _CardContent, CardHeader as _CardHeader, CardTitle as _CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent as _TabsContent, TabsList as _TabsList, TabsTrigger as _TabsTrigger } from '@/components/ui/tabs'
// import { useResizablePanel } from '@/hooks/use-resizable-panel' // Removed resizing functionality
import { KnowledgeGenerator } from './knowledge-generator'
import {
  Send,
  X,
  Bot,
  User,
  Trash2 as _Trash2,
  <PERSON><PERSON><PERSON>,
  Users,
  MapPin,
  Lightbulb,
  Plus,
  Edit3,
  <PERSON>,
  Feather,
  Zap as _Zap,
  Heart,
  Sword,
  Globe,
  Clock,
  Target,
  Pa<PERSON>,
  <PERSON>tings as _Settings,
  FileText,
  Layers,
  GitBranch,
  Wand2
} from 'lucide-react'
import ReactMarkdown from 'react-markdown'

interface KnowledgeItem {
  id: string
  type: 'character' | 'location' | 'story-arc' | 'theme' | 'conflict' | 'world-building' | 'timeline' | 'setting' | 'plot-device' | 'research' | 'note'
  title: string
  content: string
  tags: string[]
  createdAt: Date
  updatedAt: Date
  category?: string
  importance?: 'low' | 'medium' | 'high'
  connections?: string[] // IDs of related items
}

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  context?: {
    selectedText?: string
    knowledgeItems?: string[]
  }
}

interface KnowledgeInputPanelProps {
  projectId: string
  chapterId?: string
  selectedText?: string
  onClose?: () => void
}

export function KnowledgeInputPanel({
  projectId,
  chapterId: _chapterId,
  selectedText,
  onClose
}: KnowledgeInputPanelProps) {
  // Removed resizing functionality - now uses full width of parent container

  // Removed activeTab state - now only shows Knowledge Base
  const [activeKnowledgeCategory, setActiveKnowledgeCategory] = useState<string>('all')
  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeItem[]>([])
  // Removed chat-related state
  const [editingItem, setEditingItem] = useState<string | null>(null)
  const [showGenerator, setShowGenerator] = useState(false)
  const [newItem, setNewItem] = useState<{
    type: KnowledgeItem['type']
    title: string
    content: string
    tags: string[]
    importance: 'low' | 'medium' | 'high'
  }>({
    type: 'note',
    title: '',
    content: '',
    tags: [],
    importance: 'medium'
  })

  // Removed chat-related refs

  // Knowledge categories with comprehensive story elements
  const knowledgeCategories = [
    { id: 'all', label: 'All Items', icon: Layers, color: 'text-warm-600' },
    { id: 'character', label: 'Characters', icon: Users, color: 'text-blue-600' },
    { id: 'location', label: 'Locations', icon: MapPin, color: 'text-green-600' },
    { id: 'story-arc', label: 'Story Arcs', icon: GitBranch, color: 'text-purple-600' },
    { id: 'theme', label: 'Themes', icon: Heart, color: 'text-red-600' },
    { id: 'conflict', label: 'Conflicts', icon: Sword, color: 'text-orange-600' },
    { id: 'world-building', label: 'World Building', icon: Globe, color: 'text-cyan-600' },
    { id: 'timeline', label: 'Timeline', icon: Clock, color: 'text-indigo-600' },
    { id: 'setting', label: 'Settings', icon: Palette, color: 'text-pink-600' },
    { id: 'plot-device', label: 'Plot Devices', icon: Target, color: 'text-yellow-600' },
    { id: 'research', label: 'Research', icon: FileText, color: 'text-gray-600' },
    { id: 'note', label: 'General Notes', icon: Lightbulb, color: 'text-amber-600' }
  ]

  // Removed chat-related useEffect

  // Removed chat auto-scroll useEffect

  // Removed handleSendMessage function - AI chat moved to separate tab



  const addKnowledgeItem = () => {
    if (!newItem.title.trim() || !newItem.content.trim()) return

    const item: KnowledgeItem = {
      id: `item_${Date.now()}`,
      ...newItem,
      createdAt: new Date(),
      updatedAt: new Date(),
      connections: []
    }

    setKnowledgeItems(prev => [...prev, item])
    setNewItem({
      type: 'note',
      title: '',
      content: '',
      tags: [],
      importance: 'medium'
    })
  }

  const filteredKnowledgeItems = knowledgeItems.filter(item =>
    activeKnowledgeCategory === 'all' || item.type === activeKnowledgeCategory
  )

  const handleGeneratedItems = (items: KnowledgeItem[]) => {
    setKnowledgeItems(prev => [...prev, ...items])
  }

  const handleEditItem = (itemId: string) => {
    const item = knowledgeItems.find(i => i.id === itemId)
    if (item) {
      setNewItem({
        type: item.type,
        title: item.title,
        content: item.content,
        tags: item.tags,
        importance: item.importance || 'medium'
      })
      setEditingItem(itemId)
    }
  }

  const handleUpdateItem = () => {
    if (!editingItem || !newItem.title.trim() || !newItem.content.trim()) return

    setKnowledgeItems(prev => prev.map(item =>
      item.id === editingItem
        ? { ...item, ...newItem, updatedAt: new Date() }
        : item
    ))

    setEditingItem(null)
    setNewItem({
      type: 'note',
      title: '',
      content: '',
      tags: [],
      importance: 'medium'
    })
  }

  const handleCancelEdit = () => {
    setEditingItem(null)
    setNewItem({
      type: 'note',
      title: '',
      content: '',
      tags: [],
      importance: 'medium'
    })
  }

  const getTypeIcon = (type: KnowledgeItem['type']) => {
    const category = knowledgeCategories.find(cat => cat.id === type)
    if (category) {
      const IconComponent = category.icon
      return <IconComponent className="h-4 w-4" />
    }
    return <Lightbulb className="h-4 w-4" />
  }

  const getTypeColor = (type: KnowledgeItem['type']) => {
    const category = knowledgeCategories.find(cat => cat.id === type)
    if (category) {
      return `bg-${category.color.split('-')[1]}-100 ${category.color} border-${category.color.split('-')[1]}-200`
    }
    return 'bg-warm-100 text-warm-700 border-warm-200'
  }

  const getImportanceColor = (importance: 'low' | 'medium' | 'high') => {
    switch (importance) {
      case 'high': return 'bg-red-100 text-red-700 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      case 'low': return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  return (
    <div className="h-full flex flex-col bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm shadow-lg relative rounded-none border-0 border-l">

      <div className="border-b border-warm-200/50 dark:border-gray-700/50 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Feather className="h-5 w-5 text-literary-amber" />
            <h2 className="text-lg font-mono font-semibold text-warm-800 dark:text-gray-200">
              Knowledge Base
            </h2>
            {knowledgeItems.length === 0 && (
              <Button
                size="sm"
                onClick={() => setShowGenerator(true)}
                className="ml-auto bg-blue-600 hover:bg-blue-700 text-white font-mono"
              >
                <Wand2 className="h-3 w-3 mr-1" />
                Initialize
              </Button>
            )}
          </div>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose} className="text-warm-600 hover:text-warm-800 dark:text-gray-400 dark:hover:text-gray-200">
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="flex-1 flex flex-col">
        {/* Knowledge Base - now the only content */}

        {/* Removed chat section - moved to AI Chat tab */}

        {/* Knowledge Base Content - now always visible */}
        <div className="flex-1 flex flex-col">
            {/* Knowledge Categories */}
            <div className="border-b border-warm-200/50 dark:border-gray-700/50 p-4">
              <div className="grid grid-cols-2 gap-2 mb-4">
                {knowledgeCategories.map((category) => {
                  const IconComponent = category.icon
                  const isActive = activeKnowledgeCategory === category.id
                  return (
                    <button
                      key={category.id}
                      onClick={() => setActiveKnowledgeCategory(category.id)}
                      className={`flex items-center gap-2 px-3 py-2 rounded-none text-xs font-mono transition-colors ${
                        isActive
                          ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700'
                          : 'bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700'
                      }`}
                    >
                      <IconComponent className="h-3 w-3" />
                      {category.label}
                      <span className="ml-auto text-xs opacity-60">
                        {filteredKnowledgeItems.length > 0 && activeKnowledgeCategory === category.id
                          ? filteredKnowledgeItems.length
                          : knowledgeItems.filter(item => item.type === category.id).length || 0}
                      </span>
                    </button>
                  )
                })}
              </div>
            </div>

            {/* Knowledge Items */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {/* Add New Item Form */}
                <div className="border border-gray-200 dark:border-gray-700 rounded-none bg-gray-50 dark:bg-gray-800 p-4">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <select
                        value={newItem.type}
                        onChange={(e) => setNewItem(prev => ({ ...prev, type: e.target.value as KnowledgeItem['type'] }))}
                        className="px-3 py-2 rounded-none border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-900 text-sm font-mono"
                      >
                        {knowledgeCategories.slice(1).map(category => (
                          <option key={category.id} value={category.id}>{category.label}</option>
                        ))}
                      </select>
                      <select
                        value={newItem.importance}
                        onChange={(e) => setNewItem(prev => ({ ...prev, importance: e.target.value as 'low' | 'medium' | 'high' }))}
                        className="px-3 py-2 rounded-none border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-900 text-sm font-mono"
                      >
                        <option value="low">Low Priority</option>
                        <option value="medium">Medium Priority</option>
                        <option value="high">High Priority</option>
                      </select>
                    </div>
                    <Input
                      placeholder="Enter character name, location, or item title..."
                      value={newItem.title}
                      onChange={(e) => setNewItem(prev => ({ ...prev, title: e.target.value }))}
                      className="text-sm font-mono bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-600 rounded-none"
                    />
                    <Textarea
                      placeholder="Add detailed description, backstory, or notes about this story element..."
                      value={newItem.content}
                      onChange={(e) => setNewItem(prev => ({ ...prev, content: e.target.value }))}
                      className="min-h-[80px] text-sm font-mono bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-600 rounded-none"
                    />
                    <div className="flex gap-2">
                      <Button
                        onClick={editingItem ? handleUpdateItem : addKnowledgeItem}
                        size="sm"
                        className="bg-green-600 hover:bg-green-700 text-white font-mono rounded-none"
                        disabled={!newItem.title.trim() || !newItem.content.trim()}
                      >
                        {editingItem ? (
                          <>
                            <Save className="h-4 w-4 mr-1" />
                            Update Item
                          </>
                        ) : (
                          <>
                            <Plus className="h-4 w-4 mr-1" />
                            Add to Story
                          </>
                        )}
                      </Button>
                      {editingItem && (
                        <Button
                          onClick={handleCancelEdit}
                          size="sm"
                          variant="outline"
                          className="font-mono"
                        >
                          Cancel
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Knowledge Items List */}
                {filteredKnowledgeItems.length === 0 ? (
                  <div className="text-center text-gray-600 dark:text-gray-400 py-8">
                    <BookOpen className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p className="font-mono text-lg mb-2">No items in {activeKnowledgeCategory === 'all' ? 'knowledge base' : activeKnowledgeCategory}</p>
                    <p className="text-sm font-mono">Start building your story&apos;s knowledge base</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredKnowledgeItems.map((item) => (
                      <div key={item.id} className="border border-gray-200 dark:border-gray-700 rounded-none bg-white dark:bg-gray-900 hover:shadow-md transition-shadow">
                        <div className="p-4">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center gap-2">
                              {getTypeIcon(item.type)}
                              <h4 className="font-mono font-semibold text-gray-800 dark:text-gray-200">{item.title}</h4>
                              <Badge className={`${getTypeColor(item.type)} font-mono text-xs rounded-none`}>
                                {item.type}
                              </Badge>
                              {item.importance && (
                                <Badge className={`${getImportanceColor(item.importance)} font-mono text-xs rounded-none`}>
                                  {item.importance}
                                </Badge>
                              )}
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditItem(item.id)}
                              className="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
                            >
                              <Edit3 className="h-3 w-3" />
                            </Button>
                          </div>
                          <pre className="text-sm text-gray-700 dark:text-gray-300 font-mono leading-relaxed whitespace-pre-wrap bg-gray-50 dark:bg-gray-800 p-3 rounded border">
                            {item.content}
                          </pre>
                          <div className="flex items-center justify-between mt-3 text-xs text-gray-500 dark:text-gray-400 font-mono">
                            <span>last modified: {item.updatedAt.toLocaleDateString()}</span>
                            <span>id: {item.id.slice(-8)}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        </Tabs>
      </div>

      {/* Knowledge Generator Modal */}
      {showGenerator && (
        <KnowledgeGenerator
          projectId={projectId}
          onGenerated={handleGeneratedItems}
          onClose={() => setShowGenerator(false)}
        />
      )}
    </div>
  )
}
