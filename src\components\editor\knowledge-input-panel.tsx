'use client'

import { useState } from 'react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'

import { KnowledgeGenerator } from './knowledge-generator'
import {
  X,
  BookOpen,
  Users,
  MapPin,
  Lightbulb,
  Plus,
  Edit3,
  Save,
  Feather,
  Heart,
  Sword,
  Globe,
  Clock,
  Target,
  Palette,
  FileText,
  Layers,
  GitBranch,
  Wand2
} from 'lucide-react'

interface KnowledgeItem {
  id: string
  type: 'character' | 'location' | 'story-arc' | 'theme' | 'conflict' | 'world-building' | 'timeline' | 'setting' | 'plot-device' | 'research' | 'note'
  title: string
  content: string
  tags: string[]
  createdAt: Date
  updatedAt: Date
  category?: string
  importance?: 'low' | 'medium' | 'high'
  connections?: string[] // IDs of related items
}


interface KnowledgeInputPanelProps {
  projectId: string
  chapterId?: string
  selectedText?: string
  onClose?: () => void
}

export function KnowledgeInputPanel({
  projectId,
  chapterId: _chapterId,
  selectedText,
  onClose
}: KnowledgeInputPanelProps) {

  const [activeKnowledgeCategory, setActiveKnowledgeCategory] = useState<string>('all')
  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeItem[]>([])
  const [editingItem, setEditingItem] = useState<string | null>(null)
  const [showGenerator, setShowGenerator] = useState(false)
  const [newItem, setNewItem] = useState<{
    type: KnowledgeItem['type']
    title: string
    content: string
    tags: string[]
    importance: 'low' | 'medium' | 'high'
  }>({
    type: 'note',
    title: '',
    content: '',
    tags: [],
    importance: 'medium'
  })


  // Knowledge categories with comprehensive story elements
  const knowledgeCategories = [
    { id: 'all', label: 'All Items', icon: Layers },
    { id: 'character', label: 'Characters', icon: Users },
    { id: 'location', label: 'Locations', icon: MapPin },
    { id: 'story-arc', label: 'Story Arcs', icon: GitBranch },
    { id: 'theme', label: 'Themes', icon: Heart },
    { id: 'conflict', label: 'Conflicts', icon: Sword },
    { id: 'world-building', label: 'World Building', icon: Globe },
    { id: 'timeline', label: 'Timeline', icon: Clock },
    { id: 'setting', label: 'Settings', icon: Palette },
    { id: 'plot-device', label: 'Plot Devices', icon: Target },
    { id: 'research', label: 'Research', icon: FileText },
    { id: 'note', label: 'General Notes', icon: Lightbulb }
  ]



  const addKnowledgeItem = () => {
    if (!newItem.title.trim() || !newItem.content.trim()) return

    const item: KnowledgeItem = {
      id: `item_${Date.now()}`,
      ...newItem,
      createdAt: new Date(),
      updatedAt: new Date(),
      connections: []
    }

    setKnowledgeItems(prev => [...prev, item])
    setNewItem({
      type: 'note',
      title: '',
      content: '',
      tags: [],
      importance: 'medium'
    })
  }

  const filteredKnowledgeItems = knowledgeItems.filter(item =>
    activeKnowledgeCategory === 'all' || item.type === activeKnowledgeCategory
  )

  const handleGeneratedItems = (items: KnowledgeItem[]) => {
    setKnowledgeItems(prev => [...prev, ...items])
  }

  const handleEditItem = (itemId: string) => {
    const item = knowledgeItems.find(i => i.id === itemId)
    if (item) {
      setNewItem({
        type: item.type,
        title: item.title,
        content: item.content,
        tags: item.tags,
        importance: item.importance || 'medium'
      })
      setEditingItem(itemId)
    }
  }

  const handleUpdateItem = () => {
    if (!editingItem || !newItem.title.trim() || !newItem.content.trim()) return

    setKnowledgeItems(prev => prev.map(item =>
      item.id === editingItem
        ? { ...item, ...newItem, updatedAt: new Date() }
        : item
    ))

    setEditingItem(null)
    setNewItem({
      type: 'note',
      title: '',
      content: '',
      tags: [],
      importance: 'medium'
    })
  }

  const handleCancelEdit = () => {
    setEditingItem(null)
    setNewItem({
      type: 'note',
      title: '',
      content: '',
      tags: [],
      importance: 'medium'
    })
  }

  const getTypeIcon = (type: KnowledgeItem['type']) => {
    const category = knowledgeCategories.find(cat => cat.id === type)
    if (category) {
      const IconComponent = category.icon
      return <IconComponent className="h-4 w-4" />
    }
    return <Lightbulb className="h-4 w-4" />
  }

  const getTypeColor = (type: KnowledgeItem['type']) => {
    // Use muted backgrounds with primary accents for all types
    return 'bg-muted text-muted-foreground border-border'
  }

  const getImportanceColor = (importance: 'low' | 'medium' | 'high') => {
    switch (importance) {
      case 'high': return 'bg-destructive/20 text-destructive border-destructive/30'
      case 'medium': return 'bg-primary/20 text-primary border-primary/30'
      case 'low': return 'bg-muted text-muted-foreground border-border'
      default: return 'bg-muted text-muted-foreground border-border'
    }
  }

  return (
    <div className="h-full flex flex-col border-border bg-background/95 backdrop-blur-sm shadow-lg relative">

      <div className="border-b border-border p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Feather className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-mono font-semibold text-foreground">
              Knowledge Base
            </h2>
            {knowledgeItems.length === 0 && (
              <Button
                size="sm"
                onClick={() => setShowGenerator(true)}
                className="ml-auto bg-primary hover:bg-primary/90 text-primary-foreground font-mono rounded-none"
              >
                <Wand2 className="h-3 w-3 mr-1" />
                Initialize
              </Button>
            )}
          </div>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose} className="text-muted-foreground hover:text-foreground">
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="flex-1 flex flex-col">
        {/* Knowledge Categories */}
        <div className="border-b border-border p-4">
          <div className="grid grid-cols-2 gap-2 mb-4">
            {knowledgeCategories.map((category) => {
              const IconComponent = category.icon
              const isActive = activeKnowledgeCategory === category.id
              return (
                <button
                  key={category.id}
                  onClick={() => setActiveKnowledgeCategory(category.id)}
                  className={`flex items-center gap-2 px-3 py-2 rounded-none text-xs font-mono transition-colors ${
                    isActive
                      ? 'bg-primary/20 text-primary border border-primary/50'
                      : 'bg-muted text-muted-foreground hover:bg-accent hover:text-accent-foreground border border-border'
                  }`}
                >
                  <IconComponent className="h-3 w-3" />
                  {category.label}
                  <span className="ml-auto text-xs opacity-60">
                    {filteredKnowledgeItems.length > 0 && activeKnowledgeCategory === category.id
                      ? filteredKnowledgeItems.length
                      : knowledgeItems.filter(item => item.type === category.id).length || 0}
                  </span>
                </button>
              )
            })}
          </div>
        </div>

        {/* Knowledge Items */}
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            {/* Add New Item Form */}
            <div className="border border-border rounded-none bg-muted p-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                      <select
                        value={newItem.type}
                        onChange={(e) => setNewItem(prev => ({ ...prev, type: e.target.value as KnowledgeItem['type'] }))}
                        className="px-3 py-2 rounded-none border border-border bg-background text-sm font-mono"
                      >
                        {knowledgeCategories.slice(1).map(category => (
                          <option key={category.id} value={category.id}>{category.label}</option>
                        ))}
                      </select>
                      <select
                        value={newItem.importance}
                        onChange={(e) => setNewItem(prev => ({ ...prev, importance: e.target.value as 'low' | 'medium' | 'high' }))}
                        className="px-3 py-2 rounded-none border border-border bg-background text-sm font-mono"
                      >
                        <option value="low">Low Priority</option>
                        <option value="medium">Medium Priority</option>
                        <option value="high">High Priority</option>
                      </select>
                    </div>
                    <Input
                      placeholder="Enter character name, location, or item title..."
                      value={newItem.title}
                      onChange={(e) => setNewItem(prev => ({ ...prev, title: e.target.value }))}
                      className="text-sm font-mono bg-background border-border rounded-none"
                    />
                    <Textarea
                      placeholder="Add detailed description, backstory, or notes about this story element..."
                      value={newItem.content}
                      onChange={(e) => setNewItem(prev => ({ ...prev, content: e.target.value }))}
                      className="min-h-[80px] text-sm font-mono bg-background border-border rounded-none"
                    />
                    <div className="flex gap-2">
                      <Button
                        onClick={editingItem ? handleUpdateItem : addKnowledgeItem}
                        size="sm"
                        className="bg-primary hover:bg-primary/90 text-primary-foreground font-mono rounded-none"
                        disabled={!newItem.title.trim() || !newItem.content.trim()}
                      >
                        {editingItem ? (
                          <>
                            <Save className="h-4 w-4 mr-1" />
                            Update Item
                          </>
                        ) : (
                          <>
                            <Plus className="h-4 w-4 mr-1" />
                            Add to Story
                          </>
                        )}
                      </Button>
                      {editingItem && (
                        <Button
                          onClick={handleCancelEdit}
                          size="sm"
                          variant="outline"
                          className="font-mono rounded-none border-border"
                        >
                          Cancel
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Knowledge Items List */}
                {filteredKnowledgeItems.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    <BookOpen className="h-12 w-12 mx-auto mb-3 opacity-50 text-primary" />
                    <p className="font-mono text-base mb-2 text-foreground">No items in {activeKnowledgeCategory === 'all' ? 'knowledge base' : activeKnowledgeCategory}</p>
                    <p className="text-sm font-mono text-muted-foreground">Start building your story&apos;s knowledge base</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredKnowledgeItems.map((item) => (
                      <div key={item.id} className="border border-border rounded-none bg-card hover:shadow-sm transition-all hover:border-primary/50">
                        <div className="p-4">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center gap-2">
                              {getTypeIcon(item.type)}
                              <h4 className="font-mono font-semibold text-card-foreground">{item.title}</h4>
                              <Badge className={`${getTypeColor(item.type)} font-mono text-xs rounded-none`}>
                                {item.type}
                              </Badge>
                              {item.importance && (
                                <Badge className={`${getImportanceColor(item.importance)} font-mono text-xs rounded-none`}>
                                  {item.importance}
                                </Badge>
                              )}
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditItem(item.id)}
                              className="text-muted-foreground hover:text-primary font-mono"
                            >
                              <Edit3 className="h-3 w-3" />
                            </Button>
                          </div>
                          <pre className="text-sm text-card-foreground font-mono leading-relaxed whitespace-pre-wrap bg-muted p-3 rounded-none border border-border">
                            {item.content}
                          </pre>
                          <div className="flex items-center justify-between mt-3 text-xs text-muted-foreground font-mono">
                            <span>last modified: {item.updatedAt.toLocaleDateString()}</span>
                            <span>id: {item.id.slice(-8)}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        </div>

      {/* Knowledge Generator Modal */}
      {showGenerator && (
        <KnowledgeGenerator
          projectId={projectId}
          onGenerated={handleGeneratedItems}
          onClose={() => setShowGenerator(false)}
        />
      )}
    </div>
  )
}
