'use client'

import { useState, useRef as _useRef, useEffect as _useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input as _Input } from '@/components/ui/input'
import { Textarea as _Textarea } from '@/components/ui/textarea'
import { Card as _Card, CardContent as _CardContent, CardHeader as _CardHeader, CardTitle as _CardTitle } from '@/components/ui/card'
import { ScrollArea as _ScrollArea } from '@/components/ui/scroll-area'
import { Badge as _Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
// import { useResizablePanel } from '@/hooks/use-resizable-panel' // Removed resizing functionality
import {
  Send as _Send,
  X,
  <PERSON><PERSON> as _Bo<PERSON>,
  User as _User,
  Trash2 as _Trash2,
  Book<PERSON><PERSON>,
  Users as _Users,
  MapPin as _MapPin,
  Lightbulb as _Lightbulb,
  Plus as _Plus,
  Edit3 as _Edit3,
  Save as _Save,
  Feather as _Feather,
  Brain,
  FileText,
  MessageSquare,
  Star as _Star,
  TrendingUp,
  History as _History,
  Layers as _Layers,
  GitBranch as _GitBranch,
  PanelRightClose,
  PanelRightOpen,
  LineChart,
  CheckCircle2,
  BarChart3,
  Heart,
  AlertTriangle
} from 'lucide-react'
import _ReactMarkdown from 'react-markdown'
import { KnowledgeInputPanel } from './knowledge-input-panel'
import { LazyStoryBiblePanel, LazyAiChatPanel } from '@/components/lazy'
import { BookSummaryGenerator } from '@/components/analysis/book-summary-generator'
import { CharacterArcVisualizer } from '@/components/analysis/character-arc-visualizer'
import { PlotHoleDetector } from '@/components/analysis/plot-hole-detector'
import { PacingAnalyzer } from '@/components/analysis/pacing-analyzer'
import { EmotionalJourneyMapper } from '@/components/analysis/emotional-journey-mapper'
import { VoiceAnalysisPanel } from './voice-analysis-panel'
import { ConsistencyChecker } from './consistency-checker'

interface EnhancedSidebarPanelProps {
  projectId: string
  chapterId?: string
  selectedText?: string
  content?: string
  userId?: string
  onClose?: () => void
  defaultTab?: string
  onTabChange?: (tab: string) => void
}

export function EnhancedSidebarPanel({
  projectId,
  chapterId,
  selectedText,
  content,
  userId: _userId,
  onClose,
  defaultTab = 'knowledge',
  onTabChange
}: EnhancedSidebarPanelProps) {
  const [activeTab, setActiveTab] = useState(defaultTab)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [activeAnalysisTab, setActiveAnalysisTab] = useState('voice')
  const [isMobile, setIsMobile] = useState(false)
  const [panelWidth, setPanelWidth] = useState('w-96') // Fixed responsive widths

  // Handle responsive behavior
  _useEffect(() => {
    const checkScreenSize = () => {
      const mobile = window.innerWidth < 768
      const tablet = window.innerWidth < 1024
      
      setIsMobile(mobile)
      
      // Set responsive width classes
      if (mobile) {
        setPanelWidth('w-full')
      } else if (tablet) {
        setPanelWidth('w-80')
      } else {
        setPanelWidth('w-96')
      }
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])
  
  // Check if we're in demo mode
  // const _isDemo = isDemoMode()
  // const _supabase = createClient()

  // Handle collapsed state
  if (isCollapsed) {
    return (
      <div className="h-full w-12 border-l bg-background flex flex-col items-center py-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(false)}
          className="mb-4"
        >
          <PanelRightOpen className="h-4 w-4" />
        </Button>
        
        <div className="flex-1 flex flex-col items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setIsCollapsed(false)
              setActiveTab('knowledge')
            }}
            className="p-2"
          >
            <Brain className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setIsCollapsed(false)
              setActiveTab('summaries')
            }}
            className="p-2"
          >
            <FileText className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setIsCollapsed(false)
              setActiveTab('story-bible')
            }}
            className="p-2"
          >
            <BookOpen className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setIsCollapsed(false)
              setActiveTab('ai-chat')
            }}
            className="p-2"
          >
            <MessageSquare className="h-4 w-4" />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Mobile overlay backdrop */}
      {isMobile && !isCollapsed && (
        <div
          className="fixed inset-0 bg-black/50 z-40"
          onClick={onClose}
        />
      )}

      <div
        className={`
          h-full flex flex-col bg-card text-card-foreground border-l border-border
          relative transition-all duration-200 ease-out font-literary
          ${isMobile ? 'fixed inset-y-0 right-0 z-50 shadow-xl' : panelWidth}
        `}
      >

        {/* Header */}
        <div className="border-b border-border bg-card p-4 flex-shrink-0">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold font-sans text-card-foreground">
              Project Tools
            </h2>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsCollapsed(true)}
                title="Collapse panel"
              >
                <PanelRightClose className="h-4 w-4" />
              </Button>
              {onClose && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  title="Close panel"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-hidden flex flex-col">
          <Tabs value={activeTab} onValueChange={(value) => { setActiveTab(value); onTabChange?.(value); }} className="h-full flex flex-col">
            <TooltipProvider>
              <TabsList className="grid w-full grid-cols-5 gap-1 px-4 mt-2">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value="knowledge" className="p-2">
                      <Brain className="h-5 w-5" />
                    </TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Knowledge Base</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value="summaries" className="p-2">
                      <FileText className="h-5 w-5" />
                    </TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Book Summary</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value="analysis" className="p-2">
                      <TrendingUp className="h-5 w-5" />
                    </TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Analysis Tools</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value="story-bible" className="p-2">
                      <BookOpen className="h-5 w-5" />
                    </TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Story Bible</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value="ai-chat" className="p-2">
                      <MessageSquare className="h-5 w-5" />
                    </TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>AI Chat Assistant</p>
                  </TooltipContent>
                </Tooltip>
              </TabsList>
            </TooltipProvider>

            {/* Knowledge Base Tab */}
            <TabsContent value="knowledge" className="flex-1 mt-0 overflow-hidden">
              <KnowledgeInputPanel
                projectId={projectId}
                chapterId={chapterId}
                selectedText={selectedText}
              />
            </TabsContent>

            {/* Summaries Tab */}
            <TabsContent value="summaries" className="flex-1 mt-2 overflow-hidden">
              <div className="h-full px-4 pb-4 overflow-auto">
                <BookSummaryGenerator
                  projectId={projectId}
                  bookTitle="Sample Book"
                  totalWordCount={50000}
                  chapterCount={10}
                  className="h-full"
                />
              </div>
            </TabsContent>

            {/* Analysis Tab */}
            <TabsContent value="analysis" className="flex-1 mt-0 overflow-hidden">
              <div className="h-full flex flex-col">
                <Tabs value={activeAnalysisTab} onValueChange={setActiveAnalysisTab} className="h-full flex flex-col">
                  <TooltipProvider>
                    <TabsList className="grid w-full grid-cols-6 gap-1 px-4 mt-2">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TabsTrigger value="voice" className="p-2">
                            <_Feather className="h-4 w-4" />
                          </TabsTrigger>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Voice Analysis</p>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TabsTrigger value="consistency" className="p-2">
                            <CheckCircle2 className="h-4 w-4" />
                          </TabsTrigger>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Consistency Check</p>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TabsTrigger value="arcs" className="p-2">
                            <LineChart className="h-4 w-4" />
                          </TabsTrigger>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Character Arcs</p>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TabsTrigger value="pacing" className="p-2">
                            <BarChart3 className="h-4 w-4" />
                          </TabsTrigger>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Pacing Analysis</p>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TabsTrigger value="emotions" className="p-2">
                            <Heart className="h-4 w-4" />
                          </TabsTrigger>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Emotional Journey</p>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <TabsTrigger value="issues" className="p-2">
                            <AlertTriangle className="h-4 w-4" />
                          </TabsTrigger>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Plot Issues</p>
                        </TooltipContent>
                        </Tooltip>
                    </TabsList>
                  </TooltipProvider>

                  <TabsContent value="voice" className="flex-1 mt-2 overflow-hidden">
                    <div className="h-full px-4 pb-4 overflow-auto">
                      <VoiceAnalysisPanel
                        content={content || selectedText || ''}
                        projectId={projectId}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="consistency" className="flex-1 mt-2 overflow-hidden">
                    <div className="h-full px-4 pb-4 overflow-auto">
                      <ConsistencyChecker
                        projectId={projectId}
                        chapterId={chapterId}
                        content={content || selectedText || ''}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="arcs" className="flex-1 mt-2 overflow-hidden">
                    <div className="h-full px-4 pb-4 overflow-auto">
                      <CharacterArcVisualizer
                        projectId={projectId}
                        characterArcs={[]}
                        className="h-full"
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="pacing" className="flex-1 mt-2 overflow-hidden">
                    <div className="h-full px-4 pb-4 overflow-auto">
                      <PacingAnalyzer
                        projectId={projectId}
                        pacingData={{
                          overallScore: 75,
                          tensionCurve: [],
                          pacingIssues: [],
                          recommendations: []
                        }}
                        className="h-full"
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="emotions" className="flex-1 mt-2 overflow-hidden">
                    <div className="h-full px-4 pb-4 overflow-auto">
                      <EmotionalJourneyMapper
                        projectId={projectId}
                        emotionalData={[]}
                        className="h-full"
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="issues" className="flex-1 mt-2 overflow-hidden">
                    <div className="h-full px-4 pb-4 overflow-auto">
                      <PlotHoleDetector
                        projectId={projectId}
                        plotHoles={[]}
                        className="h-full"
                      />
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </TabsContent>

            {/* Story Bible Tab */}
            <TabsContent value="story-bible" className="flex-1 mt-0 overflow-hidden">
              <LazyStoryBiblePanel
                projectId={projectId}
              />
            </TabsContent>

            {/* AI Chat Tab */}
            <TabsContent value="ai-chat" className="flex-1 mt-0 overflow-hidden">
              <LazyAiChatPanel
                projectId={projectId}
                chapterId={chapterId}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </>
  )
}