import { STRIPE_PRICES } from './stripe'

export interface SubscriptionTier {
  id: string
  name: string
  description: string
  price: number
  currency: string
  interval: 'month' | 'year'
  features: string[]
  limits: {
    projects: number
    aiGenerations: number
    exportFormats: string[]
    storageGB: number
    collaborators: number
  }
  stripePriceId: string
  popular?: boolean
}

export const SUBSCRIPTION_TIERS: SubscriptionTier[] = [
  {
    id: 'storyteller',
    name: 'Storyteller',
    description: 'Perfect for trying out BookScribe AI',
    price: 0,
    currency: 'usd',
    interval: 'month',
    features: [
      '1 Active Project',
      '5 AI Generations per month',
      'Basic export (TXT)',
      'Community support',
      'Basic editor features',
      '500MB storage'
    ],
    limits: {
      projects: 1,
      aiGenerations: 5,
      exportFormats: ['txt'],
      storageGB: 0.5,
      collaborators: 0
    },
    stripePriceId: ''
  },
  {
    id: 'wordsmith',
    name: 'Wordsmith',
    description: 'For aspiring writers getting started',
    price: 9,
    currency: 'usd',
    interval: 'month',
    features: [
      '3 Active Projects',
      '50 AI Generations per month',
      'Export formats (TXT, DOCX)',
      'Email support',
      'Knowledge Base',
      'Story Bible (read-only)',
      'Basic AI Writing Assistant',
      '2GB storage'
    ],
    limits: {
      projects: 3,
      aiGenerations: 50,
      exportFormats: ['txt', 'docx'],
      storageGB: 2,
      collaborators: 0
    },
    stripePriceId: STRIPE_PRICES.basic
  },
  {
    id: 'novelist',
    name: 'Novelist',
    description: 'For dedicated writers crafting their stories',
    price: 29,
    currency: 'usd',
    interval: 'month',
    features: [
      '10 Active Projects',
      '300 AI Generations per month',
      'Export formats (TXT, DOCX, PDF)',
      'Priority email support',
      'Full Story Bible Editor',
      'Character Profiles & Development',
      '3 AI Agents (Story, Character, Writing)',
      'Voice Consistency Checker',
      '10GB storage'
    ],
    limits: {
      projects: 10,
      aiGenerations: 300,
      exportFormats: ['txt', 'docx', 'pdf'],
      storageGB: 10,
      collaborators: 0
    },
    stripePriceId: STRIPE_PRICES.pro,
    popular: true
  },
  {
    id: 'professional',
    name: 'Professional',
    description: 'For professional authors and small teams',
    price: 49,
    currency: 'usd',
    interval: 'month',
    features: [
      '25 Active Projects',
      '1,000 AI Generations per month',
      'All export formats (TXT, DOCX, PDF, EPUB)',
      'Priority chat support',
      'All 7 AI Agents',
      'Character Relationship Visualization',
      'Advanced Analytics',
      'Collaboration (up to 3 members)',
      '50GB storage'
    ],
    limits: {
      projects: 25,
      aiGenerations: 1000,
      exportFormats: ['txt', 'docx', 'pdf', 'epub'],
      storageGB: 50,
      collaborators: 3
    },
    stripePriceId: STRIPE_PRICES.enterprise
  },
  {
    id: 'literary-master',
    name: 'Literary Master',
    description: 'For publishing houses and writing teams',
    price: 99,
    currency: 'usd',
    interval: 'month',
    features: [
      'Unlimited Projects',
      '3,000 AI Generations per month',
      'All exports + custom templates',
      'White-glove support',
      'All AI Agents with priority',
      'Custom AI fine-tuning',
      'API access',
      'Team collaboration (up to 20)',
      'Publishing tools & ISBN',
      'Beta features',
      '200GB storage'
    ],
    limits: {
      projects: -1, // unlimited
      aiGenerations: 3000,
      exportFormats: ['txt', 'docx', 'pdf', 'epub', 'custom'],
      storageGB: 200,
      collaborators: 20
    },
    stripePriceId: '' // TODO: Add new Stripe price ID
  }
]

export interface UserSubscription {
  id: string
  userId: string
  tierId: string
  status: 'active' | 'canceled' | 'past_due' | 'incomplete'
  stripeSubscriptionId?: string
  stripeCustomerId?: string
  currentPeriodStart: Date
  currentPeriodEnd: Date
  cancelAtPeriodEnd: boolean
  usage: {
    aiGenerations: number
    projects: number
    storage: number
  }
  createdAt: Date
  updatedAt: Date
}

export function getTierById(tierId: string): SubscriptionTier | undefined {
  return SUBSCRIPTION_TIERS.find(tier => tier.id === tierId)
}

export function getUserTier(subscription: UserSubscription | null): SubscriptionTier {
  if (!subscription || subscription.status !== 'active') {
    const freeTier = SUBSCRIPTION_TIERS[0]
    if (!freeTier) {
      throw new Error('Free tier not found in subscription tiers')
    }
    return freeTier
  }
  const tier = getTierById(subscription.tierId) || SUBSCRIPTION_TIERS[0]
  if (!tier) {
    throw new Error('No valid subscription tier found')
  }
  return tier
}

export function checkUsageLimit(
  subscription: UserSubscription | null,
  limitType: keyof SubscriptionTier['limits'],
  currentUsage: number
): { allowed: boolean; limit: number; remaining: number } {
  const tier = getUserTier(subscription)
  const limit = tier.limits[limitType]
  
  if (typeof limit === 'number') {
    if (limit === -1) {
      // Unlimited
      return { allowed: true, limit: -1, remaining: -1 }
    }
    
    const remaining = Math.max(0, limit - currentUsage)
    return {
      allowed: currentUsage < limit,
      limit,
      remaining
    }
  }
  
  // For array limits (like exportFormats), check if the feature is included
  if (Array.isArray(limit)) {
    return {
      allowed: true,
      limit: limit.length,
      remaining: limit.length - currentUsage
    }
  }
  
  return { allowed: false, limit: 0, remaining: 0 }
}

export function canUseFeature(
  subscription: UserSubscription | null,
  feature: string
): boolean {
  const tier = getUserTier(subscription)
  
  // Map features to tier checks
  switch (feature) {
    case 'knowledge_base':
      return ['wordsmith', 'novelist', 'professional', 'literary-master'].includes(tier.id)
    case 'story_bible_read':
      return ['wordsmith', 'novelist', 'professional', 'literary-master'].includes(tier.id)
    case 'story_bible_edit':
      return ['novelist', 'professional', 'literary-master'].includes(tier.id)
    case 'character_profiles':
      return ['novelist', 'professional', 'literary-master'].includes(tier.id)
    case 'ai_agents_basic': // Story, Character, Writing agents
      return ['novelist', 'professional', 'literary-master'].includes(tier.id)
    case 'ai_agents_all': // All 7 agents
      return ['professional', 'literary-master'].includes(tier.id)
    case 'character_visualization':
      return ['professional', 'literary-master'].includes(tier.id)
    case 'advanced_analytics':
      return ['professional', 'literary-master'].includes(tier.id)
    case 'collaboration':
      return ['professional', 'literary-master'].includes(tier.id)
    case 'api_access':
      return tier.id === 'literary-master'
    case 'custom_ai_training':
      return tier.id === 'literary-master'
    case 'publishing_tools':
      return tier.id === 'literary-master'
    default:
      return true
  }
}

export function formatPrice(price: number, currency: string = 'usd'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase()
  }).format(price)
}